#!/bin/bash

# 麻将伙伴生产环境部署脚本
# 使用方法: ./deploy.sh [version]

set -e

# 配置
PROJECT_NAME="mahjong-mates"
DEPLOY_DIR="/opt/${PROJECT_NAME}"
BACKUP_DIR="/opt/${PROJECT_NAME}/backups"
LOG_FILE="/var/log/${PROJECT_NAME}/deploy.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a $LOG_FILE
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a $LOG_FILE
    exit 1
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
    fi
}

# 创建必要目录
create_directories() {
    log "创建部署目录..."
    mkdir -p $DEPLOY_DIR
    mkdir -p $BACKUP_DIR
    mkdir -p /var/log/${PROJECT_NAME}
    mkdir -p /opt/${PROJECT_NAME}/logs
    mkdir -p /opt/${PROJECT_NAME}/uploads
}

# 备份当前版本
backup_current() {
    if [ -d "$DEPLOY_DIR/current" ]; then
        log "备份当前版本..."
        BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
        cp -r $DEPLOY_DIR/current $BACKUP_DIR/$BACKUP_NAME
        log "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# 部署新版本
deploy_new_version() {
    local version=${1:-"latest"}
    log "部署新版本: $version"
    
    # 停止服务
    log "停止服务..."
    cd $DEPLOY_DIR
    docker-compose -f docker-compose.prod.yml down
    
    # 拉取新镜像
    log "拉取新镜像..."
    docker-compose -f docker-compose.prod.yml pull
    
    # 启动服务
    log "启动服务..."
    docker-compose -f docker-compose.prod.yml up -d
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    # 健康检查
    health_check
}

# 健康检查
health_check() {
    log "执行健康检查..."
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:19898/actuator/health > /dev/null 2>&1; then
            log "健康检查通过"
            return 0
        fi
        
        warn "健康检查失败 (尝试 $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    error "健康检查失败，部署回滚"
}

# 回滚
rollback() {
    log "开始回滚..."
    local latest_backup=$(ls -t $BACKUP_DIR | head -n1)
    
    if [ -z "$latest_backup" ]; then
        error "没有找到备份文件"
    fi
    
    log "回滚到: $latest_backup"
    rm -rf $DEPLOY_DIR/current
    cp -r $BACKUP_DIR/$latest_backup $DEPLOY_DIR/current
    
    cd $DEPLOY_DIR
    docker-compose -f docker-compose.prod.yml up -d
    
    health_check
    log "回滚完成"
}

# 清理旧备份
cleanup_backups() {
    log "清理旧备份..."
    find $BACKUP_DIR -type d -mtime +7 -exec rm -rf {} \;
    log "清理完成"
}

# 主函数
main() {
    log "开始部署 ${PROJECT_NAME}"
    
    check_permissions
    create_directories
    backup_current
    
    if deploy_new_version $1; then
        log "部署成功"
        cleanup_backups
    else
        warn "部署失败，开始回滚"
        rollback
    fi
    
    log "部署流程完成"
}

# 脚本入口
case "${1:-deploy}" in
    "deploy")
        main $2
        ;;
    "rollback")
        rollback
        ;;
    "health")
        health_check
        ;;
    *)
        echo "使用方法: $0 {deploy|rollback|health} [version]"
        exit 1
        ;;
esac
