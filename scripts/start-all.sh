#!/bin/bash

# 麻将伙伴全栈应用启动脚本
# 使用方法: ./start-all.sh [dev|prod] [frontend|backend|all]

set -e

# 配置
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"
}

# 显示启动信息
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "        麻将伙伴全栈应用启动脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [环境] [服务]"
    echo
    echo "环境选项:"
    echo "  dev     - 开发环境 (默认)"
    echo "  prod    - 生产环境"
    echo
    echo "服务选项:"
    echo "  frontend - 仅启动前端服务"
    echo "  backend  - 仅启动后端服务"
    echo "  all      - 启动所有服务 (默认)"
    echo
    echo "示例:"
    echo "  $0                    # 开发环境启动所有服务"
    echo "  $0 dev frontend       # 开发环境仅启动前端"
    echo "  $0 prod all           # 生产环境启动所有服务"
    echo
}

# 检查脚本权限
check_script_permissions() {
    local script_path="$1"
    if [ ! -x "$script_path" ]; then
        log "设置脚本执行权限: $script_path"
        chmod +x "$script_path"
    fi
}

# 启动后端服务
start_backend() {
    local env="$1"
    
    log "启动后端服务 ($env 环境)..."
    
    cd "$BACKEND_DIR"
    
    if [ "$env" = "dev" ]; then
        local script_path="$BACKEND_DIR/scripts/start-dev.sh"
        check_script_permissions "$script_path"
        
        info "启动开发环境后端服务..."
        bash "$script_path" &
        BACKEND_PID=$!
        
    elif [ "$env" = "prod" ]; then
        local script_path="$BACKEND_DIR/scripts/start-prod.sh"
        check_script_permissions "$script_path"
        
        info "启动生产环境后端服务..."
        sudo bash "$script_path" start
        
    else
        error "未知环境: $env"
    fi
}

# 启动前端服务
start_frontend() {
    local env="$1"
    
    log "启动前端服务 ($env 环境)..."
    
    cd "$FRONTEND_DIR"
    
    if [ "$env" = "dev" ]; then
        local script_path="$FRONTEND_DIR/scripts/start-dev.sh"
        check_script_permissions "$script_path"
        
        info "启动开发环境前端服务..."
        bash "$script_path" &
        FRONTEND_PID=$!
        
    elif [ "$env" = "prod" ]; then
        local script_path="$FRONTEND_DIR/scripts/start-prod.sh"
        check_script_permissions "$script_path"
        
        info "启动生产环境前端服务..."
        sudo bash "$script_path" deploy
        
    else
        error "未知环境: $env"
    fi
}

# 等待服务启动
wait_for_service() {
    local service_name="$1"
    local url="$2"
    local max_attempts=30
    local attempt=1
    
    log "等待 $service_name 服务启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$url" > /dev/null 2>&1; then
            info "$service_name 服务启动成功"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    warn "$service_name 服务启动超时"
    return 1
}

# 显示服务状态
show_service_status() {
    local env="$1"
    
    echo
    log "服务状态检查..."
    
    if [ "$env" = "dev" ]; then
        # 检查后端服务
        if curl -f http://localhost:19898/actuator/health > /dev/null 2>&1; then
            info "✅ 后端服务: http://localhost:19898 (运行中)"
        else
            warn "❌ 后端服务: http://localhost:19898 (未运行)"
        fi
        
        # 检查前端服务
        if curl -f http://localhost:3000 > /dev/null 2>&1; then
            info "✅ 前端服务: http://localhost:3000 (运行中)"
        else
            warn "❌ 前端服务: http://localhost:3000 (未运行)"
        fi
        
    elif [ "$env" = "prod" ]; then
        # 检查后端服务
        if curl -f http://localhost:19898/actuator/health > /dev/null 2>&1; then
            info "✅ 后端服务: http://localhost:19898 (运行中)"
        else
            warn "❌ 后端服务: http://localhost:19898 (未运行)"
        fi
        
        # 检查前端服务
        if systemctl is-active --quiet nginx; then
            info "✅ 前端服务: Nginx (运行中)"
        else
            warn "❌ 前端服务: Nginx (未运行)"
        fi
    fi
}

# 开发环境启动
start_dev_environment() {
    local service="$1"
    
    log "启动开发环境..."
    
    case "$service" in
        "backend")
            start_backend "dev"
            wait_for_service "后端" "http://localhost:19898/actuator/health"
            ;;
        "frontend")
            start_frontend "dev"
            wait_for_service "前端" "http://localhost:3000"
            ;;
        "all")
            # 先启动后端
            start_backend "dev"
            wait_for_service "后端" "http://localhost:19898/actuator/health"
            
            # 再启动前端
            sleep 3
            start_frontend "dev"
            wait_for_service "前端" "http://localhost:3000"
            ;;
        *)
            error "未知服务: $service"
            ;;
    esac
    
    show_service_status "dev"
    
    if [ "$service" = "all" ] || [ "$service" = "frontend" ]; then
        echo
        info "开发环境访问地址:"
        info "  - 前端: http://localhost:3000"
        info "  - 后端API: http://localhost:19898"
        info "  - 健康检查: http://localhost:19898/actuator/health"
        echo
        info "停止服务: Ctrl+C"
        
        # 等待用户中断
        trap 'echo -e "\n${YELLOW}正在停止所有服务...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT TERM
        wait
    fi
}

# 生产环境启动
start_prod_environment() {
    local service="$1"
    
    log "启动生产环境..."
    
    # 检查权限
    if [[ $EUID -ne 0 ]]; then
        error "生产环境部署需要root权限"
    fi
    
    case "$service" in
        "backend")
            start_backend "prod"
            ;;
        "frontend")
            start_frontend "prod"
            ;;
        "all")
            start_backend "prod"
            sleep 5
            start_frontend "prod"
            ;;
        *)
            error "未知服务: $service"
            ;;
    esac
    
    show_service_status "prod"
    
    echo
    info "生产环境访问地址:"
    info "  - 前端: https://your-domain.com"
    info "  - 后端API: https://your-domain.com/api"
    info "  - 健康检查: http://localhost:19898/actuator/health"
    echo
}

# 主函数
main() {
    show_banner
    
    local env="${1:-dev}"
    local service="${2:-all}"
    
    # 验证参数
    if [ "$env" != "dev" ] && [ "$env" != "prod" ]; then
        error "无效的环境参数: $env (支持: dev, prod)"
    fi
    
    if [ "$service" != "frontend" ] && [ "$service" != "backend" ] && [ "$service" != "all" ]; then
        error "无效的服务参数: $service (支持: frontend, backend, all)"
    fi
    
    info "环境: $env"
    info "服务: $service"
    echo
    
    # 根据环境启动服务
    if [ "$env" = "dev" ]; then
        start_dev_environment "$service"
    elif [ "$env" = "prod" ]; then
        start_prod_environment "$service"
    fi
}

# 处理帮助参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 脚本入口
main "$@"
