#!/bin/bash

# 麻将伙伴前端生产环境启动脚本
# 使用方法: ./start-prod.sh [build|deploy|start|stop|restart|status]

set -e

# 配置
PROJECT_NAME="MahjongMates Frontend"
BUILD_DIR="dist"
DEPLOY_DIR="/var/www/mahjong-mates"
NGINX_CONFIG="/etc/nginx/sites-available/mahjong-mates"
NGINX_ENABLED="/etc/nginx/sites-enabled/mahjong-mates"
BACKUP_DIR="/opt/backups/mahjong-mates-frontend"
LOG_DIR="/var/log/mahjong-mates"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a $LOG_DIR/frontend-deploy.log
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a $LOG_DIR/frontend-deploy.log
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a $LOG_DIR/frontend-deploy.log
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a $LOG_DIR/frontend-deploy.log
}

# 显示启动信息
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    麻将伙伴前端服务 - 生产环境管理脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
    fi
}

# 创建必要目录
create_directories() {
    mkdir -p $DEPLOY_DIR
    mkdir -p $BACKUP_DIR
    mkdir -p $LOG_DIR
    mkdir -p /var/www/uploads
}

# 检查Node.js环境
check_node() {
    log "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        error "Node.js未安装"
    fi
    
    NODE_VER=$(node -v)
    info "Node.js版本: $NODE_VER"
}

# 检查Nginx
check_nginx() {
    log "检查Nginx环境..."
    
    if ! command -v nginx &> /dev/null; then
        error "Nginx未安装"
    fi
    
    if ! systemctl is-active --quiet nginx; then
        warn "Nginx服务未运行"
    else
        info "Nginx服务运行正常"
    fi
}

# 创建生产环境配置
create_prod_config() {
    log "创建生产环境配置..."
    
    cat > .env.production << EOF
# 生产环境配置
NODE_ENV=production
VUE_APP_API_BASE_URL=https://your-domain.com
VUE_APP_WEBSOCKET_URL=wss://your-domain.com
VUE_APP_UPLOAD_URL=https://your-domain.com/uploads

# 微信小程序配置
VUE_APP_WECHAT_APP_ID=${WECHAT_APP_ID:-your-wechat-app-id}

# 生产配置
VUE_APP_DEBUG=false
VUE_APP_LOG_LEVEL=error
EOF
    
    info "已创建生产环境配置文件"
}

# 构建项目
build_project() {
    log "构建前端项目..."
    
    # 切换到项目目录
    cd "$(dirname "$0")/.."
    
    check_node
    
    # 检查配置文件
    if [ ! -f ".env.production" ]; then
        create_prod_config
    fi
    
    # 清理旧的构建文件
    if [ -d "$BUILD_DIR" ]; then
        rm -rf $BUILD_DIR
        log "已清理旧的构建文件"
    fi
    
    # 安装依赖
    log "安装生产依赖..."
    npm ci --only=production
    
    # 构建项目
    log "开始构建项目..."
    npm run build
    
    if [ ! -d "$BUILD_DIR" ]; then
        error "构建失败，未生成dist目录"
    fi
    
    info "项目构建成功"
}

# 备份当前版本
backup_current() {
    if [ -d "$DEPLOY_DIR" ] && [ "$(ls -A $DEPLOY_DIR)" ]; then
        log "备份当前版本..."
        BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
        cp -r $DEPLOY_DIR $BACKUP_DIR/$BACKUP_NAME
        log "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# 部署到生产环境
deploy_to_production() {
    log "部署到生产环境..."
    
    backup_current
    
    # 复制构建文件
    log "复制构建文件到部署目录..."
    rm -rf $DEPLOY_DIR/*
    cp -r $BUILD_DIR/* $DEPLOY_DIR/
    
    # 设置权限
    chown -R www-data:www-data $DEPLOY_DIR
    chmod -R 755 $DEPLOY_DIR
    
    info "文件部署完成"
}

# 配置Nginx
configure_nginx() {
    log "配置Nginx..."
    
    cat > $NGINX_CONFIG << EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    root $DEPLOY_DIR;
    index index.html;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:19898;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 文件上传
    location /uploads/ {
        alias /var/www/uploads/;
        expires 30d;
    }
    
    # SPA路由
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
    
    # 启用站点
    ln -sf $NGINX_CONFIG $NGINX_ENABLED
    
    # 测试配置
    nginx -t
    if [ $? -ne 0 ]; then
        error "Nginx配置测试失败"
    fi
    
    info "Nginx配置完成"
}

# 启动服务
start_service() {
    log "启动前端服务..."
    
    check_nginx
    
    # 重新加载Nginx配置
    systemctl reload nginx
    
    # 检查服务状态
    if systemctl is-active --quiet nginx; then
        log "前端服务启动成功"
        show_service_info
    else
        error "前端服务启动失败"
    fi
}

# 停止服务
stop_service() {
    log "停止前端服务..."
    
    # 禁用站点
    if [ -L "$NGINX_ENABLED" ]; then
        rm -f $NGINX_ENABLED
        systemctl reload nginx
        log "前端服务已停止"
    else
        warn "前端服务未运行"
    fi
}

# 重启服务
restart_service() {
    log "重启前端服务..."
    stop_service
    sleep 2
    start_service
}

# 显示服务状态
show_status() {
    echo -e "${BLUE}前端服务状态:${NC}"
    
    if [ -L "$NGINX_ENABLED" ]; then
        echo "  状态: 运行中"
        echo "  配置文件: $NGINX_CONFIG"
        echo "  部署目录: $DEPLOY_DIR"
        echo "  访问地址: https://your-domain.com"
    else
        echo "  状态: 已停止"
    fi
    
    echo "  Nginx状态: $(systemctl is-active nginx)"
    echo "  部署日志: $LOG_DIR/frontend-deploy.log"
}

# 显示服务信息
show_service_info() {
    echo
    info "前端服务访问地址:"
    info "  - HTTPS: https://your-domain.com"
    info "  - HTTP: http://your-domain.com (自动重定向到HTTPS)"
    echo
    info "服务状态:"
    info "  - Nginx配置: $NGINX_CONFIG"
    info "  - 部署目录: $DEPLOY_DIR"
    info "  - 日志文件: $LOG_DIR/frontend-deploy.log"
    echo
}

# 清理旧备份
cleanup_backups() {
    log "清理旧备份..."
    find $BACKUP_DIR -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    log "清理完成"
}

# 主函数
main() {
    show_banner
    check_permissions
    create_directories
    
    case "${1:-deploy}" in
        "build")
            build_project
            ;;
        "deploy")
            build_project
            deploy_to_production
            configure_nginx
            start_service
            cleanup_backups
            ;;
        "start")
            start_service
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            restart_service
            ;;
        "status")
            show_status
            ;;
        *)
            echo "使用方法: $0 {build|deploy|start|stop|restart|status}"
            echo "  build   - 构建项目"
            echo "  deploy  - 构建并部署到生产环境"
            echo "  start   - 启动前端服务"
            echo "  stop    - 停止前端服务"
            echo "  restart - 重启前端服务"
            echo "  status  - 显示服务状态"
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
