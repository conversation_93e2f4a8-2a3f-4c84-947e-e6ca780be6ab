2025-07-17 14:25:23.943 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 24451 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-17 14:25:23.946 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-17 14:25:23.946 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-17 14:25:23.967 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-17 14:25:23.967 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-17 14:25:24.448 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-17 14:25:24.449 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-17 14:25:24.461 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-07-17 14:25:24.845 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 19898 (http)
2025-07-17 14:25:24.853 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-17 14:25:24.853 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-17 14:25:24.875 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-17 14:25:24.875 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 908 ms
2025-07-17 14:25:25.027 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e4da9717-9708-4b11-b07f-838d96e39a6e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-17 14:25:25.352 [restartedMain] ERROR i.n.resolver.dns.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-17 14:25:25.457 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-17 14:25:25.492 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@54fe8d01, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@61874602, org.springframework.security.web.context.SecurityContextHolderFilter@586564f3, org.springframework.security.web.header.HeaderWriterFilter@2477d46d, org.springframework.web.filter.CorsFilter@f54a92c, org.springframework.security.web.authentication.logout.LogoutFilter@50747474, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@197113fb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@670f5a15, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@55fdcb4e, org.springframework.security.web.access.ExceptionTranslationFilter@46610a48, org.springframework.security.web.access.intercept.AuthorizationFilter@11e49501]
2025-07-17 14:25:25.752 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-17 14:25:25.777 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 19898 (http) with context path ''
2025-07-17 14:25:25.789 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 2.035 seconds (process running for 2.229)
