2025-07-10 09:17:57.122 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 91477 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:17:57.123 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:17:57.124 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:17:57.149 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:17:57.149 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:17:57.667 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:17:57.669 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:17:57.686 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-07-10 09:17:57.743 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/FriendshipMapper.class]
2025-07-10 09:17:57.743 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Ignored because not a concrete top-level class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameMapper$GameStatistics.class]
2025-07-10 09:17:57.743 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameMapper.class]
2025-07-10 09:17:57.743 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameRecordMapper.class]
2025-07-10 09:17:57.744 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/UserMapper.class]
2025-07-10 09:17:57.744 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'friendshipMapper' and 'com.mahjongmates.mapper.FriendshipMapper' mapperInterface
2025-07-10 09:17:57.745 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'friendshipMapper'.
2025-07-10 09:17:57.745 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gameMapper' and 'com.mahjongmates.mapper.GameMapper' mapperInterface
2025-07-10 09:17:57.745 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gameMapper'.
2025-07-10 09:17:57.745 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gameRecordMapper' and 'com.mahjongmates.mapper.GameRecordMapper' mapperInterface
2025-07-10 09:17:57.745 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gameRecordMapper'.
2025-07-10 09:17:57.745 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.mahjongmates.mapper.UserMapper' mapperInterface
2025-07-10 09:17:57.745 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-10 09:17:57.746 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-07-10 09:17:57.756 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 09:17:57.766 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:836)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:620)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:575)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:534)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:606)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347)
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-07-10 09:18:45.483 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 92409 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:18:45.484 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:18:45.484 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:18:45.510 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:18:45.511 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:18:46.019 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:18:46.021 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:18:46.035 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-07-10 09:18:46.091 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/FriendshipMapper.class]
2025-07-10 09:18:46.091 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Ignored because not a concrete top-level class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameMapper$GameStatistics.class]
2025-07-10 09:18:46.091 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameMapper.class]
2025-07-10 09:18:46.091 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameRecordMapper.class]
2025-07-10 09:18:46.091 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/UserMapper.class]
2025-07-10 09:18:46.091 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'friendshipMapper' and 'com.mahjongmates.mapper.FriendshipMapper' mapperInterface
2025-07-10 09:18:46.093 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'friendshipMapper'.
2025-07-10 09:18:46.093 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gameMapper' and 'com.mahjongmates.mapper.GameMapper' mapperInterface
2025-07-10 09:18:46.093 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gameMapper'.
2025-07-10 09:18:46.093 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gameRecordMapper' and 'com.mahjongmates.mapper.GameRecordMapper' mapperInterface
2025-07-10 09:18:46.093 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gameRecordMapper'.
2025-07-10 09:18:46.093 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.mahjongmates.mapper.UserMapper' mapperInterface
2025-07-10 09:18:46.093 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-10 09:18:46.093 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-07-10 09:18:46.104 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 09:18:46.113 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:836)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:620)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:575)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:534)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:606)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347)
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-07-10 09:25:16.413 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 99638 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:25:16.413 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:25:16.414 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:25:16.436 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:25:16.436 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:25:16.955 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:25:16.956 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:25:16.971 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-07-10 09:25:17.031 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/FriendshipMapper.class]
2025-07-10 09:25:17.032 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Ignored because not a concrete top-level class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameMapper$GameStatistics.class]
2025-07-10 09:25:17.032 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameMapper.class]
2025-07-10 09:25:17.032 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/GameRecordMapper.class]
2025-07-10 09:25:17.032 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/mapper/UserMapper.class]
2025-07-10 09:25:17.032 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'friendshipMapper' and 'com.mahjongmates.mapper.FriendshipMapper' mapperInterface
2025-07-10 09:25:17.033 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'friendshipMapper'.
2025-07-10 09:25:17.033 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gameMapper' and 'com.mahjongmates.mapper.GameMapper' mapperInterface
2025-07-10 09:25:17.033 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gameMapper'.
2025-07-10 09:25:17.033 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gameRecordMapper' and 'com.mahjongmates.mapper.GameRecordMapper' mapperInterface
2025-07-10 09:25:17.033 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gameRecordMapper'.
2025-07-10 09:25:17.033 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.mahjongmates.mapper.UserMapper' mapperInterface
2025-07-10 09:25:17.033 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-10 09:25:17.034 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-07-10 09:25:17.044 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 09:25:17.053 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:836)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:620)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:575)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:534)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:606)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347)
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-07-10 09:26:51.758 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 1697 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:26:51.758 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:26:51.759 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:26:51.779 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:26:51.779 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:26:52.080 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalStateException: Error processing condition on org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration.propertySourcesPlaceholderConfigurer
2025-07-10 09:26:52.087 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 09:26:52.108 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: Error processing condition on org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration.propertySourcesPlaceholderConfigurer
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:183)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:144)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:428)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:289)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:606)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347)
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.mahjongmates.config.MybatisPlusConfig] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@7c2019ae]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:360)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$1(AbstractAutowireCapableBeanFactory.java:749)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:748)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:681)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:652)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1644)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:562)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:534)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:247)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:240)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:230)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:183)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:158)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 22 common frames omitted
Caused by: java.lang.NoClassDefFoundError: MybatisPlusInterceptor
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2504)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 38 common frames omitted
Caused by: java.lang.ClassNotFoundException: MybatisPlusInterceptor
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	... 42 common frames omitted
2025-07-10 09:30:13.727 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 5542 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:30:13.728 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:30:13.728 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:30:13.750 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:30:13.750 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:30:14.230 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:30:14.231 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:30:14.245 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-07-10 09:30:14.608 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 09:30:14.615 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 09:30:14.615 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 09:30:14.640 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 09:30:14.640 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 890 ms
2025-07-10 09:30:14.735 [restartedMain] DEBUG com.mahjongmates.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-10 09:30:14.741 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mybatisPlusConfig.MyMetaObjectHandler': Lookup method resolution failed
2025-07-10 09:30:14.742 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-10 09:30:14.785 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 09:30:14.793 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mybatisPlusConfig.MyMetaObjectHandler': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:497)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:367)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1293)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:561)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:960)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347)
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.mahjongmates.config.MybatisPlusConfig$MyMetaObjectHandler] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@261842b2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:320)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:475)
	... 24 common frames omitted
Caused by: java.lang.NoClassDefFoundError: MetaObject
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2504)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 26 common frames omitted
Caused by: java.lang.ClassNotFoundException: MetaObject
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	... 30 common frames omitted
2025-07-10 09:31:34.386 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 7093 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:31:34.387 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:31:34.387 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:31:34.407 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:31:34.407 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:31:34.906 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:31:34.907 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:31:34.921 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-07-10 09:31:35.331 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 09:31:35.338 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 09:31:35.338 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 09:31:35.361 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 09:31:35.361 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 953 ms
2025-07-10 09:31:35.444 [restartedMain] DEBUG com.mahjongmates.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-10 09:31:35.454 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/controller/AuthController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'userServiceImpl' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/service/impl/UserServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.mahjongmates.mapper.UserMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-10 09:31:35.455 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-10 09:31:35.497 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 09:31:35.506 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.mahjongmates.service.impl.UserServiceImpl required a bean of type 'com.mahjongmates.mapper.UserMapper' that could not be found.


Action:

Consider defining a bean of type 'com.mahjongmates.mapper.UserMapper' in your configuration.

2025-07-10 09:43:50.560 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 20865 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:43:50.560 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:43:50.561 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:43:50.580 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:43:50.580 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:43:51.040 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:43:51.041 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:43:51.051 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-07-10 09:43:51.414 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 09:43:51.420 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 09:43:51.420 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 09:43:51.446 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 09:43:51.446 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 865 ms
2025-07-10 09:43:51.934 [restartedMain] ERROR i.n.resolver.dns.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-10 09:43:52.149 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 5ddc231b-8f29-476c-8724-6db092aa077f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 09:43:52.207 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 09:43:52.228 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2ac284d7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c535550, org.springframework.security.web.context.SecurityContextHolderFilter@3f59c278, org.springframework.security.web.header.HeaderWriterFilter@6b1154b5, org.springframework.web.filter.CorsFilter@14094e7c, org.springframework.security.web.csrf.CsrfFilter@51b62ef9, org.springframework.security.web.authentication.logout.LogoutFilter@1e5842ee, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@d927aa4, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@2e98669f, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@2722932d, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7f0f231c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1018beda, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1b88ee60, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19f84f22, org.springframework.security.web.access.ExceptionTranslationFilter@a764230, org.springframework.security.web.access.intercept.AuthorizationFilter@5ce6a1e]
2025-07-10 09:43:52.275 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 09:43:52.304 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-10 09:43:52.350 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 09:43:52.359 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-10 09:47:38.829 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 25551 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:47:38.830 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:47:38.830 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:47:38.851 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 09:47:38.851 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 09:47:39.333 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:47:39.334 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:47:39.347 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-07-10 09:47:39.725 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 09:47:39.735 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 09:47:39.736 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 09:47:39.759 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 09:47:39.759 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 907 ms
2025-07-10 09:47:40.261 [restartedMain] ERROR i.n.resolver.dns.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-10 09:47:40.467 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 9525f4fc-7a88-4a6c-ac1f-fd2218bf48f9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 09:47:40.519 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 09:47:40.541 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@10e56e83, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@600128bc, org.springframework.security.web.context.SecurityContextHolderFilter@cad2839, org.springframework.security.web.header.HeaderWriterFilter@6293781f, org.springframework.web.filter.CorsFilter@334c57f, org.springframework.security.web.csrf.CsrfFilter@33d50ab2, org.springframework.security.web.authentication.logout.LogoutFilter@551a5cf3, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@400e5f6, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@71f943d4, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@7ea5475c, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6e53d8e3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1f20cd44, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6a978ef0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@458faf7d, org.springframework.security.web.access.ExceptionTranslationFilter@1938a39f, org.springframework.security.web.access.intercept.AuthorizationFilter@6b7fd7e3]
2025-07-10 09:47:40.586 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 09:47:40.610 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 09:47:40.620 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 1.991 seconds (process running for 2.181)
2025-07-10 09:48:58.379 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 09:48:58.380 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-10 09:48:58.383 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-10 09:48:58.398 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /health
2025-07-10 09:48:58.406 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-10 09:48:58.421 [http-nio-8080-exec-1] DEBUG o.s.s.web.savedrequest.HttpSessionRequestCache - Saved request http://localhost:8080/health?continue to session
2025-07-10 09:48:58.422 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using And [Not [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[application/xhtml+xml, image/*, text/html, text/plain], useEquals=false, ignoredMediaTypes=[*/*]]]
2025-07-10 09:48:58.422 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using Or [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest], And [Not [MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[text/html], useEquals=false, ignoredMediaTypes=[]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[application/atom+xml, application/x-www-form-urlencoded, application/json, application/octet-stream, application/xml, multipart/form-data, text/xml], useEquals=false, ignoredMediaTypes=[*/*]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[*/*], useEquals=true, ignoredMediaTypes=[]]]
2025-07-10 09:48:58.422 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Match found! Executing org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint@519015c3
2025-07-10 09:48:58.422 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]
2025-07-10 09:48:58.423 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - No match found. Using default entry point org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint@980d8da
2025-07-10 09:48:58.425 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-10 09:48:58.426 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-10 09:48:58.426 [http-nio-8080-exec-1] DEBUG o.s.s.web.savedrequest.HttpSessionRequestCache - Saved request http://localhost:8080/error?continue to session
2025-07-10 09:48:58.426 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using And [Not [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[application/xhtml+xml, image/*, text/html, text/plain], useEquals=false, ignoredMediaTypes=[*/*]]]
2025-07-10 09:48:58.426 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using Or [RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest], And [Not [MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[text/html], useEquals=false, ignoredMediaTypes=[]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[application/atom+xml, application/x-www-form-urlencoded, application/json, application/octet-stream, application/xml, multipart/form-data, text/xml], useEquals=false, ignoredMediaTypes=[*/*]]], MediaTypeRequestMatcher [contentNegotiationStrategy=org.springframework.web.accept.ContentNegotiationManager@6e1677b6, matchingMediaTypes=[*/*], useEquals=true, ignoredMediaTypes=[]]]
2025-07-10 09:48:58.426 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Match found! Executing org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint@519015c3
2025-07-10 09:48:58.427 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - Trying to match using RequestHeaderRequestMatcher [expectedHeaderName=X-Requested-With, expectedHeaderValue=XMLHttpRequest]
2025-07-10 09:48:58.427 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.DelegatingAuthenticationEntryPoint - No match found. Using default entry point org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint@980d8da
2025-07-10 09:49:18.825 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-07-10 09:49:18.916 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 25551 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 09:49:18.917 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 09:49:18.917 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 09:49:19.048 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 09:49:19.049 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 09:49:19.050 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 09:49:19.308 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 09:49:19.308 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 09:49:19.308 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 09:49:19.316 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 09:49:19.316 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 398 ms
2025-07-10 09:49:19.389 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 73be747e-7c5c-4845-b3b7-81c88f20ac72

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 09:49:19.496 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 09:49:19.520 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7ce5c3ec, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@79eed794, org.springframework.security.web.context.SecurityContextHolderFilter@4de58e1, org.springframework.security.web.header.HeaderWriterFilter@41e3f90a, org.springframework.web.filter.CorsFilter@41db7261, org.springframework.security.web.authentication.logout.LogoutFilter@29e58d61, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54ee7b41, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1ff5dad, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b070dd2, org.springframework.security.web.access.ExceptionTranslationFilter@54c02099, org.springframework.security.web.access.intercept.AuthorizationFilter@759fe929]
2025-07-10 09:49:19.621 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 09:49:19.628 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 09:49:19.635 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 0.735 seconds (process running for 101.194)
2025-07-10 09:49:19.637 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation delta:


==========================
CONDITION EVALUATION DELTA
==========================


Positive matches:
-----------------

    None


Negative matches:
-----------------

   ManagementWebSecurityAutoConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.web.SecurityFilterChain' filterChain; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   SpringBootWebSecurityConfiguration.WebSecurityEnablerConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (names: springSecurityFilterChain; SearchStrategy: all) found beans named springSecurityFilterChain (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



2025-07-10 09:50:06.516 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 09:50:06.517 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-10 09:50:06.518 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-10 09:50:06.519 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /health
2025-07-10 09:50:06.521 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /health
2025-07-10 09:50:06.530 [http-nio-8080-exec-1] INFO  com.mahjongmates.controller.HealthController - 健康检查请求
2025-07-10 09:50:06.562 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-10 09:50:14.791 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /health/version
2025-07-10 09:50:14.792 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /health/version
2025-07-10 09:50:14.793 [http-nio-8080-exec-3] INFO  com.mahjongmates.controller.HealthController - 版本信息请求
2025-07-10 09:50:14.794 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-10 09:58:08.485 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /health
2025-07-10 09:58:08.489 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /health
2025-07-10 09:58:08.490 [http-nio-8080-exec-5] INFO  com.mahjongmates.controller.HealthController - 健康检查请求
2025-07-10 09:58:08.500 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-10 13:55:28.433 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-10 13:55:28.524 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 25551 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 13:55:28.525 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 13:55:28.525 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 13:55:28.703 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 13:55:28.703 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 13:55:28.704 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 13:55:28.791 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 13:55:28.792 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 13:55:28.792 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 13:55:28.799 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 13:55:28.799 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 273 ms
2025-07-10 13:55:28.865 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 44dc1c73-c6e2-4185-8296-9bd89f720fab

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 13:55:28.981 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 13:55:29.004 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@48d01c00, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4f1492b0, org.springframework.security.web.context.SecurityContextHolderFilter@7c27e693, org.springframework.security.web.header.HeaderWriterFilter@3b9767b3, org.springframework.web.filter.CorsFilter@49686cdc, org.springframework.security.web.authentication.logout.LogoutFilter@a4bc378, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@181a4a43, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@49b1fbcc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@396f5617, org.springframework.security.web.access.ExceptionTranslationFilter@363cf8d4, org.springframework.security.web.access.intercept.AuthorizationFilter@4656d422]
2025-07-10 13:55:29.114 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 13:55:29.122 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 13:55:29.129 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 0.621 seconds (process running for 14868.533)
2025-07-10 13:55:29.130 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-10 13:55:36.583 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-10 13:55:36.679 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 25551 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 13:55:36.679 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 13:55:36.679 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 13:55:36.810 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 13:55:36.810 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 13:55:36.810 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 13:55:36.880 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 13:55:36.880 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 13:55:36.880 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 13:55:36.887 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 13:55:36.887 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 207 ms
2025-07-10 13:55:36.947 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 19158aa6-d48e-4ee9-9b8d-e51b6c90f3e2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 13:55:37.052 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 13:55:37.074 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2285b68b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@29516bab, org.springframework.security.web.context.SecurityContextHolderFilter@4cb902fe, org.springframework.security.web.header.HeaderWriterFilter@4740ccdc, org.springframework.web.filter.CorsFilter@6ae04eeb, org.springframework.security.web.authentication.logout.LogoutFilter@6aec2666, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@863704, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2fa178b6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@8882ce8, org.springframework.security.web.access.ExceptionTranslationFilter@10f7fff5, org.springframework.security.web.access.intercept.AuthorizationFilter@7fa3b53a]
2025-07-10 13:55:37.175 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 13:55:37.183 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 13:55:37.189 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 0.525 seconds (process running for 14876.594)
2025-07-10 13:55:37.190 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-10 13:55:53.701 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-10 13:55:53.753 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 25551 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 13:55:53.753 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 13:55:53.753 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 13:55:53.874 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 13:55:53.874 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 13:55:53.875 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 13:55:53.942 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 13:55:53.942 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 13:55:53.942 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 13:55:53.949 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 13:55:53.949 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 195 ms
2025-07-10 13:55:54.008 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 73eb3df4-3045-426d-9176-3e43e22c25c5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 13:55:54.112 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 13:55:54.135 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@e5daead, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27a594db, org.springframework.security.web.context.SecurityContextHolderFilter@1b42cc3b, org.springframework.security.web.header.HeaderWriterFilter@1e106f99, org.springframework.web.filter.CorsFilter@1380172c, org.springframework.security.web.authentication.logout.LogoutFilter@134742c7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@723f8278, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@398bd78, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@49138f23, org.springframework.security.web.access.ExceptionTranslationFilter@2eebe7a0, org.springframework.security.web.access.intercept.AuthorizationFilter@5b50332c]
2025-07-10 13:55:54.234 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 13:55:54.243 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 13:55:54.249 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 0.508 seconds (process running for 14893.653)
2025-07-10 13:55:54.250 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-10 14:25:28.440 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-10 14:25:28.543 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 25551 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 14:25:28.543 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 14:25:28.543 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 14:25:28.694 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 14:25:28.694 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 14:25:28.695 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 14:25:28.771 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 14:25:28.771 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 14:25:28.771 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 14:25:28.777 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 14:25:28.777 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 233 ms
2025-07-10 14:25:28.837 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: eea10eac-1299-44f1-8798-d4d2b9b14360

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 14:25:28.945 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 14:25:28.968 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f0b9e23, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@271720b, org.springframework.security.web.context.SecurityContextHolderFilter@7197fb7e, org.springframework.security.web.header.HeaderWriterFilter@161827e0, org.springframework.web.filter.CorsFilter@28d1260a, org.springframework.security.web.authentication.logout.LogoutFilter@9e49667, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35e5e24, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1016db3b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6ae9ac5b, org.springframework.security.web.access.ExceptionTranslationFilter@63ae1eba, org.springframework.security.web.access.intercept.AuthorizationFilter@5ed3853e]
2025-07-10 14:25:29.067 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 14:25:29.075 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 14:25:29.081 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 0.552 seconds (process running for 16668.422)
2025-07-10 14:25:29.082 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-10 16:43:58.509 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 68277 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 16:43:58.510 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 16:43:58.511 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 16:43:58.536 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-10 16:43:58.536 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-10 16:43:59.021 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 16:43:59.022 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 16:43:59.034 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-07-10 16:43:59.402 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 16:43:59.409 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 16:43:59.409 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 16:43:59.432 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 16:43:59.432 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 896 ms
2025-07-10 16:43:59.593 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1d8bc81f-b87d-486b-b5ef-5ff0d713c68f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 16:43:59.869 [restartedMain] ERROR i.n.resolver.dns.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-10 16:43:59.970 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 16:44:00.004 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2583f259, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7518f5d6, org.springframework.security.web.context.SecurityContextHolderFilter@751e5abe, org.springframework.security.web.header.HeaderWriterFilter@668549b8, org.springframework.web.filter.CorsFilter@452568f5, org.springframework.security.web.authentication.logout.LogoutFilter@74ac6958, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@57417307, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@42edf28f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1f3243df, org.springframework.security.web.access.ExceptionTranslationFilter@56192753, org.springframework.security.web.access.intercept.AuthorizationFilter@37cce0e7]
2025-07-10 16:44:00.253 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 16:44:00.276 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 16:44:00.287 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 2.005 seconds (process running for 2.19)
2025-07-10 16:48:52.601 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 16:48:52.602 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-10 16:48:52.606 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-10 16:48:52.622 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /health
2025-07-10 16:48:52.630 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /health
2025-07-10 16:48:52.636 [http-nio-8080-exec-1] INFO  com.mahjongmates.controller.HealthController - 健康检查请求
2025-07-10 16:48:52.667 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-10 17:37:03.801 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-07-10 17:37:03.955 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 68277 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 17:37:03.956 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 17:37:03.956 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 17:37:04.126 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 17:37:04.126 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 17:37:04.128 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-07-10 17:37:04.231 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 17:37:04.231 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 17:37:04.231 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 17:37:04.238 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 17:37:04.238 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 281 ms
2025-07-10 17:37:04.270 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseTestController' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/controller/DatabaseTestController.class]: Failed to instantiate [com.mahjongmates.controller.DatabaseTestController]: Constructor threw exception
2025-07-10 17:37:04.271 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-10 17:37:04.278 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 17:37:04.287 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseTestController' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/controller/DatabaseTestController.class]: Failed to instantiate [com.mahjongmates.controller.DatabaseTestController]: Constructor threw exception
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1316) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1201) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:561) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:960) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347) ~[spring-boot-3.2.1.jar:3.2.1]
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.1.jar:3.2.1]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.mahjongmates.controller.DatabaseTestController]: Constructor threw exception
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:223) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1310) ~[spring-beans-6.1.2.jar:6.1.2]
	... 22 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The import org.springframework.jdbc cannot be resolved
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type
	The method error(String) in the type ApiResult is not applicable for the arguments (String, Map<String,Object>)

	at com.mahjongmates.controller.DatabaseTestController.<init>(DatabaseTestController.java:5) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:197) ~[spring-beans-6.1.2.jar:6.1.2]
	... 24 common frames omitted
2025-07-10 17:44:27.427 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 68277 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 17:44:27.428 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 17:44:27.428 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 17:44:27.617 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 17:44:27.617 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 17:44:27.618 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 17:44:27.695 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 17:44:27.696 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 17:44:27.696 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 17:44:27.703 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 17:44:27.703 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 273 ms
2025-07-10 17:44:27.734 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseTestController' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/controller/DatabaseTestController.class]: Failed to instantiate [com.mahjongmates.controller.DatabaseTestController]: Constructor threw exception
2025-07-10 17:44:27.734 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-10 17:44:27.741 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 17:44:27.748 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseTestController' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/controller/DatabaseTestController.class]: Failed to instantiate [com.mahjongmates.controller.DatabaseTestController]: Constructor threw exception
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1316) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1201) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:561) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:960) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347) ~[spring-boot-3.2.1.jar:3.2.1]
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.1.jar:3.2.1]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.mahjongmates.controller.DatabaseTestController]: Constructor threw exception
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:223) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1310) ~[spring-beans-6.1.2.jar:6.1.2]
	... 22 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The import org.springframework.jdbc cannot be resolved
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type
	JdbcTemplate cannot be resolved to a type

	at com.mahjongmates.controller.DatabaseTestController.<init>(DatabaseTestController.java:5) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:197) ~[spring-beans-6.1.2.jar:6.1.2]
	... 24 common frames omitted
2025-07-10 17:46:36.292 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 68277 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 17:46:36.292 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 17:46:36.292 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 17:46:36.448 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 17:46:36.448 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 17:46:36.449 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 17:46:36.582 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 17:46:36.585 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 17:46:36.586 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 17:46:36.644 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 17:46:36.645 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 351 ms
2025-07-10 17:46:36.752 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseTestController' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/controller/DatabaseTestController.class]: Post-processing of merged bean definition failed
2025-07-10 17:46:36.752 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-10 17:46:36.759 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 17:46:36.761 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseTestController' defined in file [/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes/com/mahjongmates/controller/DatabaseTestController.class]: Post-processing of merged bean definition failed
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:960) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1358) ~[spring-boot-3.2.1.jar:3.2.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1347) ~[spring-boot-3.2.1.jar:3.2.1]
	at com.mahjongmates.MahjongMatesApplication.main(MahjongMatesApplication.java:26) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.1.jar:3.2.1]
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.mahjongmates.controller.DatabaseTestController] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@70b9024f]
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:757) ~[spring-core-6.1.2.jar:6.1.2]
	at org.springframework.util.ReflectionUtils.doWithLocalFields(ReflectionUtils.java:689) ~[spring-core-6.1.2.jar:6.1.2]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.buildResourceMetadata(CommonAnnotationBeanPostProcessor.java:406) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.findResourceMetadata(CommonAnnotationBeanPostProcessor.java:387) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessMergedBeanDefinition(CommonAnnotationBeanPostProcessor.java:312) ~[spring-context-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyMergedBeanDefinitionPostProcessors(AbstractAutowireCapableBeanFactory.java:1084) ~[spring-beans-6.1.2.jar:6.1.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:573) ~[spring-beans-6.1.2.jar:6.1.2]
	... 20 common frames omitted
Caused by: java.lang.NoClassDefFoundError: org/springframework/jdbc/core/JdbcTemplate
	at java.base/java.lang.Class.getDeclaredFields0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredFields(Class.java:3297) ~[na:na]
	at java.base/java.lang.Class.getDeclaredFields(Class.java:2371) ~[na:na]
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:752) ~[spring-core-6.1.2.jar:6.1.2]
	... 26 common frames omitted
Caused by: java.lang.ClassNotFoundException: org.springframework.jdbc.core.JdbcTemplate
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:467) ~[na:na]
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121) ~[spring-boot-devtools-3.2.1.jar:3.2.1]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[na:na]
	... 30 common frames omitted
2025-07-10 17:48:09.969 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Starting MahjongMatesApplication using Java 17.0.15 with PID 68277 (/Users/<USER>/code/sky-ai/MahjongMates/backend/target/classes started by skyzhao in /Users/<USER>/code/sky-ai/MahjongMates/backend)
2025-07-10 17:48:09.969 [restartedMain] DEBUG com.mahjongmates.MahjongMatesApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-07-10 17:48:09.969 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - The following 1 profile is active: "dev"
2025-07-10 17:48:10.113 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 17:48:10.113 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 17:48:10.114 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-10 17:48:10.184 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-10 17:48:10.184 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 17:48:10.184 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-10 17:48:10.191 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 17:48:10.191 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 220 ms
2025-07-10 17:48:10.262 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 923c51c1-a033-4235-b998-6b074bebec21

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10 17:48:10.374 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-10 17:48:10.400 [restartedMain] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@73629160, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4aed1988, org.springframework.security.web.context.SecurityContextHolderFilter@5d4e0974, org.springframework.security.web.header.HeaderWriterFilter@5c5c69a8, org.springframework.web.filter.CorsFilter@79e0cf43, org.springframework.security.web.authentication.logout.LogoutFilter@253390b4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@14619762, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3735030d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@475c3220, org.springframework.security.web.access.ExceptionTranslationFilter@587f6112, org.springframework.security.web.access.intercept.AuthorizationFilter@2243983f]
2025-07-10 17:48:10.510 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-10 17:48:10.519 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-10 17:48:10.525 [restartedMain] INFO  com.mahjongmates.MahjongMatesApplication - Started MahjongMatesApplication in 0.58 seconds (process running for 3758.279)
2025-07-10 17:48:10.527 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-10 17:48:48.719 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 17:48:48.720 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-10 17:48:48.725 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-10 17:48:48.729 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /health
2025-07-10 17:48:48.731 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /health
2025-07-10 17:48:48.733 [http-nio-8080-exec-1] INFO  com.mahjongmates.controller.HealthController - 健康检查请求
2025-07-10 17:48:48.743 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-10 17:49:22.143 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /health/version
2025-07-10 17:49:22.145 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /health/version
2025-07-10 17:49:22.146 [http-nio-8080-exec-3] INFO  com.mahjongmates.controller.HealthController - 版本信息请求
2025-07-10 17:49:22.148 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
