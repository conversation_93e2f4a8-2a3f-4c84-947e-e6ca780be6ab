server:
  port: 19898
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: mahjong-mates

  profiles:
    active: dev
  
  # 数据源配置 (由各环境配置覆盖)
  # datasource:
  #   type: com.alibaba.druid.pool.DruidDataSource
  #   driver-class-name: com.mysql.cj.jdbc.Driver
  #   url: jdbc:mysql://${MYSQL_HOST:************}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:mahjong_mates}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
  #   username: ${MYSQL_USERNAME:root}
  #   password: ${MYSQL_PASSWORD:123456}
    
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123
        allow: 127.0.0.1
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # RabbitMQ配置
  rabbitmq:
    host: ${RABBITMQ_HOST:************}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:mahjong}
    password: ${RABBITMQ_PASSWORD:mahjong123}
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 1
        max-concurrency: 1
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          max-interval: 10000
          multiplier: 1.0
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
      enabled: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_uuid
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: not_null
      insert-strategy: not_null
      select-strategy: not_empty
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.mahjongmates.entity

# 日志配置
logging:
  level:
    com.mahjongmates: debug
    org.springframework.security: debug
    org.springframework.web: info
    org.mybatis: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/mahjong-mates.log
    max-size: 100MB
    max-history: 30

# 应用自定义配置
app:
  # JWT配置
  jwt:
    secret: mahjong-mates-jwt-secret-key-2024
    expiration: 604800 # 7天
    header: Authorization
    prefix: Bearer
  
  # 微信小程序配置
  wechat:
    app-id: ${WECHAT_APP_ID:}
    app-secret: ${WECHAT_APP_SECRET:}
    grant-type: authorization_code
  
  # 文件存储配置
  file:
    upload-path: /app/uploads
    access-url: /uploads
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,mp3,wav,mp4
  
  # 业务配置
  business:
    max-game-players: 4
    default-credit-score: 100
    nearby-search-radius: 5000
    message-retention-days: 30
    announcement-duration-minutes: 1
    max-horn-usage-per-day: 10
    friend-request-expire-days: 7
    game-auto-finish-hours: 24

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  # 开发环境数据源
  datasource:
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456

# 开发环境日志
logging:
  level:
    root: info
    com.mahjongmates: debug

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

# 生产环境日志
logging:
  level:
    root: warn
    com.mahjongmates: info
  file:
    name: /app/logs/mahjong-mates.log

---
# Docker环境配置
spring:
  config:
    activate:
      on-profile: docker
