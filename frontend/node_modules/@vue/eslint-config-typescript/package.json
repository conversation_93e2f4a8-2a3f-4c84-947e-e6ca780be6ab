{"name": "@vue/eslint-config-typescript", "version": "11.0.3", "description": "eslint-config-typescript for vue-cli", "main": "index.js", "files": ["index.js", "recommended.js"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-config-typescript.git"}, "keywords": ["vue", "cli", "eslint", "typescript"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/eslint-config-typescript/issues"}, "homepage": "https://github.com/vuejs/eslint-config-typescript#readme", "devDependencies": {"eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "execa": "^4.1.0", "jest": "^26.6.3", "typescript": "^4.9.5", "vue": "^2.7.14", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.1.2"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "vue-eslint-parser": "^9.1.1"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "scripts": {"test": "jest"}}