#!/bin/bash

# 麻将伙伴后端开发环境启动脚本
# 使用方法: ./start-dev.sh

set -e

# 配置
PROJECT_NAME="MahjongMates Backend"
JAVA_VERSION="17"
MAVEN_PROFILE="dev"
SERVER_PORT="19898"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"
}

# 显示启动信息
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    麻将伙伴后端服务 - 开发环境启动脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查Java环境
check_java() {
    log "检查Java环境..."
    
    # 设置JAVA_HOME
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        export JAVA_HOME=/opt/homebrew/opt/openjdk@17
        if [ ! -d "$JAVA_HOME" ]; then
            export JAVA_HOME=/usr/local/opt/openjdk@17
        fi
    else
        # Linux
        if [ -z "$JAVA_HOME" ]; then
            export JAVA_HOME=/usr/lib/jvm/java-17-openjdk
        fi
    fi
    
    export PATH="$JAVA_HOME/bin:$PATH"
    
    # 检查Java版本
    if ! command -v java &> /dev/null; then
        error "Java未安装或未配置到PATH中"
    fi
    
    JAVA_VER=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VER" != "$JAVA_VERSION" ]; then
        warn "Java版本不匹配，期望: $JAVA_VERSION, 实际: $JAVA_VER"
    else
        info "Java版本检查通过: $JAVA_VER"
    fi
}

# 检查Maven
check_maven() {
    log "检查Maven环境..."
    
    if ! command -v mvn &> /dev/null; then
        error "Maven未安装或未配置到PATH中"
    fi
    
    MVN_VER=$(mvn -version | head -n 1 | cut -d' ' -f3)
    info "Maven版本: $MVN_VER"
}

# 检查端口
check_port() {
    log "检查端口 $SERVER_PORT..."
    
    if lsof -Pi :$SERVER_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        warn "端口 $SERVER_PORT 已被占用"
        read -p "是否要停止占用端口的进程? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            PID=$(lsof -ti:$SERVER_PORT)
            kill -9 $PID
            log "已停止进程 PID: $PID"
        else
            error "端口被占用，无法启动服务"
        fi
    else
        info "端口 $SERVER_PORT 可用"
    fi
}

# 检查数据库连接
check_database() {
    log "检查数据库连接..."
    
    DB_HOST="************"
    DB_PORT="3306"
    
    if command -v nc &> /dev/null; then
        if nc -z $DB_HOST $DB_PORT 2>/dev/null; then
            info "数据库连接正常: $DB_HOST:$DB_PORT"
        else
            warn "无法连接到数据库: $DB_HOST:$DB_PORT"
        fi
    else
        warn "nc命令不可用，跳过数据库连接检查"
    fi
}

# 检查Redis连接
check_redis() {
    log "检查Redis连接..."
    
    REDIS_HOST="************"
    REDIS_PORT="6379"
    
    if command -v nc &> /dev/null; then
        if nc -z $REDIS_HOST $REDIS_PORT 2>/dev/null; then
            info "Redis连接正常: $REDIS_HOST:$REDIS_PORT"
        else
            warn "无法连接到Redis: $REDIS_HOST:$REDIS_PORT"
        fi
    else
        warn "nc命令不可用，跳过Redis连接检查"
    fi
}

# 清理和编译
build_project() {
    log "清理和编译项目..."
    
    mvn clean compile -DskipTests -q
    
    if [ $? -eq 0 ]; then
        info "项目编译成功"
    else
        error "项目编译失败"
    fi
}

# 启动应用
start_application() {
    log "启动 $PROJECT_NAME (开发环境)..."
    
    info "配置信息:"
    info "  - Profile: $MAVEN_PROFILE"
    info "  - Port: $SERVER_PORT"
    info "  - Java Home: $JAVA_HOME"
    info "  - Database: ************:3306"
    info "  - Redis: ************:6379"
    echo
    
    log "正在启动应用，请稍候..."
    
    # 启动Spring Boot应用
    mvn spring-boot:run -Dspring-boot.run.profiles=$MAVEN_PROFILE
}

# 显示启动后信息
show_startup_info() {
    echo
    log "应用启动成功！"
    echo
    info "访问地址:"
    info "  - 健康检查: http://localhost:$SERVER_PORT/actuator/health"
    info "  - API文档: http://localhost:$SERVER_PORT/health"
    info "  - 版本信息: http://localhost:$SERVER_PORT/health/version"
    echo
    info "日志位置: logs/mahjong-mates.log"
    info "停止应用: Ctrl+C"
    echo
}

# 主函数
main() {
    show_banner
    
    # 切换到backend目录
    cd "$(dirname "$0")/.."
    
    check_java
    check_maven
    check_port
    check_database
    check_redis
    build_project
    
    # 设置环境变量
    export SPRING_PROFILES_ACTIVE=$MAVEN_PROFILE
    
    # 启动应用
    start_application
}

# 信号处理
trap 'echo -e "\n${YELLOW}正在停止应用...${NC}"; exit 0' INT TERM

# 脚本入口
main "$@"
