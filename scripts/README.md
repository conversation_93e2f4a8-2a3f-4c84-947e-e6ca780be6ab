# 麻将伙伴启动脚本使用指南

## 📁 脚本目录结构

```
scripts/
├── start-all.sh                    # 统一启动脚本
└── README.md                       # 本文档

backend/scripts/
├── start-dev.sh                    # 后端开发环境启动
└── start-prod.sh                   # 后端生产环境启动

frontend/scripts/
├── start-dev.sh                    # 前端开发环境启动
└── start-prod.sh                   # 前端生产环境启动
```

## 🚀 快速启动

### 开发环境

```bash
# 启动所有服务 (推荐)
./scripts/start-all.sh

# 或者分别启动
./scripts/start-all.sh dev all

# 仅启动后端
./scripts/start-all.sh dev backend

# 仅启动前端
./scripts/start-all.sh dev frontend
```

### 生产环境

```bash
# 启动所有服务 (需要root权限)
sudo ./scripts/start-all.sh prod all

# 仅启动后端
sudo ./scripts/start-all.sh prod backend

# 仅启动前端
sudo ./scripts/start-all.sh prod frontend
```

## 🔧 单独使用脚本

### 后端脚本

#### 开发环境
```bash
cd backend
./scripts/start-dev.sh
```

#### 生产环境
```bash
cd backend
sudo ./scripts/start-prod.sh start    # 启动
sudo ./scripts/start-prod.sh stop     # 停止
sudo ./scripts/start-prod.sh restart  # 重启
sudo ./scripts/start-prod.sh status   # 状态
```

### 前端脚本

#### 开发环境
```bash
cd frontend
./scripts/start-dev.sh start    # 启动开发服务器
./scripts/start-dev.sh clean    # 清理缓存
./scripts/start-dev.sh install  # 重新安装依赖
```

#### 生产环境
```bash
cd frontend
sudo ./scripts/start-prod.sh build    # 构建项目
sudo ./scripts/start-prod.sh deploy   # 构建并部署
sudo ./scripts/start-prod.sh start    # 启动服务
sudo ./scripts/start-prod.sh stop     # 停止服务
sudo ./scripts/start-prod.sh restart  # 重启服务
sudo ./scripts/start-prod.sh status   # 查看状态
```

## 📊 环境对比

| 功能 | 开发环境 | 生产环境 |
|------|----------|----------|
| **后端启动方式** | Maven Spring Boot | JAR包 + systemd |
| **前端启动方式** | npm run dev | Nginx静态文件 |
| **端口配置** | 后端:19898, 前端:3000 | 后端:19898, 前端:80/443 |
| **数据库** | ************:3306 | 环境变量配置 |
| **日志级别** | DEBUG | WARN/ERROR |
| **热重载** | ✅ 支持 | ❌ 不支持 |
| **SSL/HTTPS** | ❌ 不需要 | ✅ 必需 |

## 🔍 脚本功能特性

### 开发环境脚本特性
- ✅ **环境检查**: Java、Node.js、Maven、npm版本检查
- ✅ **端口检查**: 自动检测端口占用并提示处理
- ✅ **依赖管理**: 自动安装和更新依赖
- ✅ **服务连接**: 检查后端API连接状态
- ✅ **配置生成**: 自动生成开发环境配置文件
- ✅ **热重载**: 支持代码修改自动重启

### 生产环境脚本特性
- ✅ **权限检查**: 确保以root权限运行
- ✅ **环境变量**: 验证必需的环境变量
- ✅ **健康检查**: 启动后自动验证服务状态
- ✅ **备份机制**: 部署前自动备份当前版本
- ✅ **日志管理**: 结构化日志记录和轮转
- ✅ **进程管理**: PID文件管理和优雅停止

## 🚨 故障排查

### 常见问题

#### 1. 权限问题
```bash
# 给脚本执行权限
chmod +x scripts/*.sh
chmod +x backend/scripts/*.sh
chmod +x frontend/scripts/*.sh
```

#### 2. 端口被占用
```bash
# 查看端口占用
lsof -i:19898  # 后端端口
lsof -i:3000   # 前端端口

# 停止占用进程
kill -9 <PID>
```

#### 3. Java环境问题
```bash
# 检查Java版本
java -version

# 设置JAVA_HOME (macOS)
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH="$JAVA_HOME/bin:$PATH"
```

#### 4. Node.js环境问题
```bash
# 检查Node版本
node --version
npm --version

# 清理npm缓存
npm cache clean --force
```

#### 5. 数据库连接问题
```bash
# 测试数据库连接
mysql -h ************ -u root -p mahjong_mates

# 检查网络连通性
ping ************
telnet ************ 3306
```

### 日志查看

#### 开发环境
```bash
# 后端日志
tail -f backend/logs/mahjong-mates.log

# 前端日志 (控制台输出)
# 直接在启动的终端查看
```

#### 生产环境
```bash
# 后端日志
tail -f /var/log/mahjong-mates/mahjong-mates.log
tail -f /var/log/mahjong-mates/startup.log

# 前端日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

## 🔧 自定义配置

### 修改端口
```bash
# 开发环境 - 修改脚本中的变量
vim backend/scripts/start-dev.sh    # SERVER_PORT="19898"
vim frontend/scripts/start-dev.sh   # DEV_PORT="3000"

# 生产环境 - 修改环境变量
export SERVER_PORT=8080
```

### 修改数据库配置
```bash
# 开发环境 - 修改 application.yml
vim backend/src/main/resources/application.yml

# 生产环境 - 设置环境变量
export MYSQL_HOST=your-db-host
export MYSQL_USERNAME=your-username
export MYSQL_PASSWORD=your-password
```

## 📞 技术支持

如果遇到问题：
1. 查看相应的日志文件
2. 检查环境变量配置
3. 验证网络连接
4. 确认服务依赖是否正常

更多帮助请参考项目文档或联系技术团队。
