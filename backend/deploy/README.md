# 麻将伙伴生产环境部署指南

## 📋 目录结构

```
deploy/
├── production.env.template     # 环境变量模板
├── docker-compose.prod.yml    # 生产环境Docker编排
├── nginx/
│   └── nginx.conf             # Nginx配置
├── scripts/
│   └── deploy.sh              # 部署脚本
├── monitoring/
│   └── prometheus.yml         # 监控配置
└── README.md                  # 本文档
```

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp production.env.template production.env

# 编辑配置文件
vim production.env
```

### 3. 部署应用

```bash
# 给部署脚本执行权限
chmod +x scripts/deploy.sh

# 执行部署
sudo ./scripts/deploy.sh deploy
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `MYSQL_HOST` | MySQL主机地址 | `mysql` |
| `MYSQL_USERNAME` | MySQL用户名 | `mahjong_user` |
| `MYSQL_PASSWORD` | MySQL密码 | `secure_password` |
| `REDIS_HOST` | Redis主机地址 | `redis` |
| `REDIS_PASSWORD` | Redis密码 | `redis_password` |
| `RABBITMQ_HOST` | RabbitMQ主机地址 | `rabbitmq` |
| `JWT_SECRET` | JWT密钥 | `your-256-bit-secret` |

### 数据库配置

生产环境数据库配置特点：
- 使用环境变量，不在代码中硬编码
- 启用SSL连接
- 优化连接池参数
- 关闭开发工具（如Druid监控页面）

### 安全配置

1. **SSL/TLS加密**
   - 数据库连接使用SSL
   - Nginx配置HTTPS
   - Redis密码保护

2. **访问控制**
   - 防火墙配置
   - 网络隔离
   - 最小权限原则

3. **密钥管理**
   - 使用环境变量
   - 定期轮换密钥
   - 避免硬编码

## 📊 监控和维护

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:19898/actuator/health

# 检查所有服务状态
docker-compose -f docker-compose.prod.yml ps
```

### 日志管理

```bash
# 查看应用日志
docker-compose -f docker-compose.prod.yml logs -f mahjong-mates-backend

# 查看Nginx日志
docker-compose -f docker-compose.prod.yml logs -f nginx
```

### 备份策略

1. **数据库备份**
   ```bash
   # 每日自动备份
   docker exec mahjong-mysql mysqldump -u root -p mahjong_mates > backup_$(date +%Y%m%d).sql
   ```

2. **文件备份**
   ```bash
   # 备份上传文件
   tar -czf uploads_backup_$(date +%Y%m%d).tar.gz ./uploads/
   ```

### 性能优化

1. **JVM参数调优**
   ```bash
   JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
   ```

2. **数据库优化**
   - 索引优化
   - 查询优化
   - 连接池调优

3. **缓存策略**
   - Redis缓存
   - 应用级缓存
   - CDN加速

## 🔄 运维操作

### 部署新版本

```bash
# 部署指定版本
sudo ./scripts/deploy.sh deploy v1.2.0

# 部署最新版本
sudo ./scripts/deploy.sh deploy
```

### 回滚操作

```bash
# 回滚到上一个版本
sudo ./scripts/deploy.sh rollback
```

### 扩容操作

```bash
# 水平扩容应用实例
docker-compose -f docker-compose.prod.yml up -d --scale mahjong-mates-backend=3
```

## 🚨 故障排查

### 常见问题

1. **服务启动失败**
   - 检查环境变量配置
   - 查看容器日志
   - 验证网络连接

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查网络连通性

3. **内存不足**
   - 调整JVM参数
   - 增加服务器内存
   - 优化应用代码

### 监控告警

配置Prometheus + Grafana监控：
- 应用性能指标
- 系统资源使用
- 业务指标监控
- 自动告警通知

## 📞 技术支持

如遇到问题，请联系技术团队：
- 邮箱: <EMAIL>
- 电话: 400-xxx-xxxx
- 文档: https://docs.mahjongmates.com
