#!/bin/bash

# 麻将伙伴后端生产环境启动脚本
# 使用方法: ./start-prod.sh [start|stop|restart|status]

set -e

# 配置
PROJECT_NAME="MahjongMates Backend"
DEPLOY_DIR="/opt/mahjong-mates"
LOG_DIR="/var/log/mahjong-mates"
PID_FILE="/var/run/mahjong-mates.pid"
JAR_FILE="mahjong-mates-backend-1.0.0.jar"
JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
SERVER_PORT="19898"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a $LOG_DIR/startup.log
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a $LOG_DIR/startup.log
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a $LOG_DIR/startup.log
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a $LOG_DIR/startup.log
}

# 显示启动信息
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    麻将伙伴后端服务 - 生产环境管理脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
    fi
}

# 创建必要目录
create_directories() {
    mkdir -p $DEPLOY_DIR
    mkdir -p $LOG_DIR
    mkdir -p /opt/mahjong-mates/logs
    mkdir -p /opt/mahjong-mates/uploads
    
    # 设置权限
    chown -R mahjong:mahjong $DEPLOY_DIR 2>/dev/null || true
    chown -R mahjong:mahjong $LOG_DIR 2>/dev/null || true
}

# 检查环境变量
check_environment() {
    log "检查环境变量..."
    
    local required_vars=(
        "MYSQL_HOST"
        "MYSQL_USERNAME" 
        "MYSQL_PASSWORD"
        "REDIS_HOST"
        "RABBITMQ_HOST"
        "JWT_SECRET"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        error "缺少必需的环境变量: ${missing_vars[*]}"
    fi
    
    info "环境变量检查通过"
}

# 检查Java环境
check_java() {
    log "检查Java环境..."
    
    if ! command -v java &> /dev/null; then
        error "Java未安装"
    fi
    
    JAVA_VER=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VER" != "17" ]; then
        error "需要Java 17，当前版本: $JAVA_VER"
    fi
    
    info "Java环境检查通过: $JAVA_VER"
}

# 检查JAR文件
check_jar_file() {
    log "检查JAR文件..."
    
    if [ ! -f "$DEPLOY_DIR/$JAR_FILE" ]; then
        error "JAR文件不存在: $DEPLOY_DIR/$JAR_FILE"
    fi
    
    info "JAR文件检查通过"
}

# 检查端口
check_port() {
    if lsof -Pi :$SERVER_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        local pid=$(lsof -ti:$SERVER_PORT)
        warn "端口 $SERVER_PORT 已被进程 $pid 占用"
        return 1
    fi
    return 0
}

# 获取应用PID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat $PID_FILE)
        if ps -p $pid > /dev/null 2>&1; then
            echo $pid
        else
            rm -f $PID_FILE
            echo ""
        fi
    else
        echo ""
    fi
}

# 检查应用状态
check_status() {
    local pid=$(get_pid)
    if [ -n "$pid" ]; then
        echo "running"
    else
        echo "stopped"
    fi
}

# 启动应用
start_application() {
    log "启动 $PROJECT_NAME (生产环境)..."
    
    local status=$(check_status)
    if [ "$status" = "running" ]; then
        warn "应用已在运行中"
        return 0
    fi
    
    # 检查环境
    check_java
    check_jar_file
    check_environment
    
    if ! check_port; then
        error "端口 $SERVER_PORT 被占用，无法启动"
    fi
    
    # 设置环境变量
    export SPRING_PROFILES_ACTIVE=prod
    
    log "启动参数:"
    info "  - Profile: prod"
    info "  - Port: $SERVER_PORT"
    info "  - JVM Options: $JAVA_OPTS"
    info "  - Log Dir: $LOG_DIR"
    
    # 启动应用
    cd $DEPLOY_DIR
    nohup java $JAVA_OPTS \
        -Dspring.profiles.active=prod \
        -Dserver.port=$SERVER_PORT \
        -Dlogging.file.name=$LOG_DIR/mahjong-mates.log \
        -jar $JAR_FILE \
        > $LOG_DIR/nohup.out 2>&1 &
    
    local pid=$!
    echo $pid > $PID_FILE
    
    # 等待启动
    log "等待应用启动..."
    local count=0
    while [ $count -lt 30 ]; do
        if curl -f http://localhost:$SERVER_PORT/actuator/health > /dev/null 2>&1; then
            log "应用启动成功! PID: $pid"
            show_startup_info
            return 0
        fi
        sleep 2
        ((count++))
    done
    
    error "应用启动超时"
}

# 停止应用
stop_application() {
    log "停止 $PROJECT_NAME..."
    
    local pid=$(get_pid)
    if [ -z "$pid" ]; then
        warn "应用未运行"
        return 0
    fi
    
    log "停止进程 PID: $pid"
    kill $pid
    
    # 等待进程停止
    local count=0
    while [ $count -lt 15 ]; do
        if ! ps -p $pid > /dev/null 2>&1; then
            log "应用已停止"
            rm -f $PID_FILE
            return 0
        fi
        sleep 1
        ((count++))
    done
    
    # 强制停止
    warn "强制停止进程"
    kill -9 $pid
    rm -f $PID_FILE
    log "应用已强制停止"
}

# 重启应用
restart_application() {
    log "重启 $PROJECT_NAME..."
    stop_application
    sleep 3
    start_application
}

# 显示应用状态
show_status() {
    local status=$(check_status)
    local pid=$(get_pid)
    
    echo -e "${BLUE}应用状态信息:${NC}"
    echo "  状态: $status"
    if [ -n "$pid" ]; then
        echo "  PID: $pid"
        echo "  端口: $SERVER_PORT"
        echo "  内存使用: $(ps -p $pid -o rss= | awk '{print $1/1024 "MB"}')"
        echo "  启动时间: $(ps -p $pid -o lstart= | xargs)"
    fi
    echo "  日志文件: $LOG_DIR/mahjong-mates.log"
    echo "  PID文件: $PID_FILE"
}

# 显示启动后信息
show_startup_info() {
    echo
    info "应用访问地址:"
    info "  - 健康检查: http://localhost:$SERVER_PORT/actuator/health"
    info "  - API接口: http://localhost:$SERVER_PORT/health"
    info "  - 监控指标: http://localhost:$SERVER_PORT/actuator/metrics"
    echo
    info "日志文件:"
    info "  - 应用日志: $LOG_DIR/mahjong-mates.log"
    info "  - 启动日志: $LOG_DIR/nohup.out"
    echo
}

# 主函数
main() {
    show_banner
    check_permissions
    create_directories
    
    case "${1:-start}" in
        "start")
            start_application
            ;;
        "stop")
            stop_application
            ;;
        "restart")
            restart_application
            ;;
        "status")
            show_status
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|status}"
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
