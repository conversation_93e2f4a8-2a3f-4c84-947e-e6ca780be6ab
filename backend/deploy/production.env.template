# 生产环境配置模板
# 复制此文件为 production.env 并填入实际值

# 应用配置
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=19898

# 数据库配置
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_DATABASE=mahjong_mates
MYSQL_USERNAME=your-mysql-user
MYSQL_PASSWORD=your-mysql-password

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DATABASE=0

# RabbitMQ配置
RABBITMQ_HOST=your-rabbitmq-host
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=your-rabbitmq-user
RABBITMQ_PASSWORD=your-rabbitmq-password
RABBITMQ_VHOST=/

# JWT配置
JWT_SECRET=your-super-secure-jwt-secret-key-for-production
JWT_EXPIRATION=604800

# 微信小程序配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 文件存储配置
FILE_UPLOAD_PATH=/app/uploads
FILE_ACCESS_URL=/uploads

# 日志配置
LOG_LEVEL_ROOT=warn
LOG_LEVEL_APP=info
LOG_FILE_PATH=/app/logs/mahjong-mates.log

# 监控配置
MANAGEMENT_ENDPOINTS_ENABLED=true
ACTUATOR_USERNAME=admin
ACTUATOR_PASSWORD=your-actuator-password
