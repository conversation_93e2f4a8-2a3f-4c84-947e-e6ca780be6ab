#!/bin/bash

# 麻将伙伴前端开发环境启动脚本
# 使用方法: ./start-dev.sh

set -e

# 配置
PROJECT_NAME="MahjongMates Frontend"
NODE_VERSION="18"
DEV_PORT="3000"
BACKEND_URL="http://localhost:19898"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"
}

# 显示启动信息
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    麻将伙伴前端服务 - 开发环境启动脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查Node.js环境
check_node() {
    log "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        error "Node.js未安装"
    fi
    
    NODE_VER=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VER" -lt "$NODE_VERSION" ]; then
        warn "Node.js版本较低，建议使用 v$NODE_VERSION+，当前版本: v$NODE_VER"
    else
        info "Node.js版本检查通过: v$NODE_VER"
    fi
}

# 检查npm
check_npm() {
    log "检查npm环境..."
    
    if ! command -v npm &> /dev/null; then
        error "npm未安装"
    fi
    
    NPM_VER=$(npm -v)
    info "npm版本: $NPM_VER"
}

# 检查端口
check_port() {
    log "检查端口 $DEV_PORT..."
    
    if lsof -Pi :$DEV_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        warn "端口 $DEV_PORT 已被占用"
        read -p "是否要停止占用端口的进程? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            PID=$(lsof -ti:$DEV_PORT)
            kill -9 $PID
            log "已停止进程 PID: $PID"
        else
            warn "端口被占用，将尝试使用其他端口"
        fi
    else
        info "端口 $DEV_PORT 可用"
    fi
}

# 检查后端服务
check_backend() {
    log "检查后端服务连接..."
    
    if curl -f $BACKEND_URL/actuator/health > /dev/null 2>&1; then
        info "后端服务连接正常: $BACKEND_URL"
    else
        warn "无法连接到后端服务: $BACKEND_URL"
        warn "请确保后端服务已启动"
    fi
}

# 检查依赖
check_dependencies() {
    log "检查项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        warn "node_modules目录不存在，需要安装依赖"
        install_dependencies
    else
        # 检查package.json是否有更新
        if [ "package.json" -nt "node_modules" ]; then
            warn "package.json已更新，重新安装依赖"
            install_dependencies
        else
            info "依赖检查通过"
        fi
    fi
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    # 清理缓存
    npm cache clean --force 2>/dev/null || true
    
    # 安装依赖
    npm install
    
    if [ $? -eq 0 ]; then
        info "依赖安装成功"
    else
        error "依赖安装失败"
    fi
}

# 检查配置文件
check_config() {
    log "检查配置文件..."
    
    # 检查是否存在环境配置文件
    if [ ! -f ".env.development" ]; then
        warn "开发环境配置文件不存在，创建默认配置"
        create_dev_config
    fi
    
    info "配置文件检查完成"
}

# 创建开发环境配置
create_dev_config() {
    cat > .env.development << EOF
# 开发环境配置
NODE_ENV=development
VUE_APP_API_BASE_URL=http://localhost:19898
VUE_APP_WEBSOCKET_URL=ws://localhost:19898
VUE_APP_UPLOAD_URL=http://localhost:19898/uploads

# 微信小程序配置
VUE_APP_WECHAT_APP_ID=your-wechat-app-id

# 调试配置
VUE_APP_DEBUG=true
VUE_APP_LOG_LEVEL=debug
EOF
    
    info "已创建开发环境配置文件: .env.development"
}

# 启动开发服务器
start_dev_server() {
    log "启动 $PROJECT_NAME (开发环境)..."
    
    info "配置信息:"
    info "  - 环境: development"
    info "  - 端口: $DEV_PORT"
    info "  - 后端API: $BACKEND_URL"
    info "  - Node版本: $(node -v)"
    info "  - npm版本: $(npm -v)"
    echo
    
    log "正在启动开发服务器，请稍候..."
    
    # 设置环境变量
    export NODE_ENV=development
    export PORT=$DEV_PORT
    
    # 启动开发服务器
    npm run dev
}

# 显示启动后信息
show_startup_info() {
    echo
    log "前端开发服务器启动成功！"
    echo
    info "访问地址:"
    info "  - 本地访问: http://localhost:$DEV_PORT"
    info "  - 网络访问: http://$(hostname -I | awk '{print $1}'):$DEV_PORT"
    echo
    info "开发工具:"
    info "  - 微信开发者工具: 导入项目目录"
    info "  - 浏览器调试: F12 开发者工具"
    echo
    info "停止服务: Ctrl+C"
    echo
}

# 清理缓存
clean_cache() {
    log "清理缓存和临时文件..."
    
    # 清理npm缓存
    npm cache clean --force 2>/dev/null || true
    
    # 清理node_modules
    if [ -d "node_modules" ]; then
        rm -rf node_modules
        log "已清理 node_modules"
    fi
    
    # 清理package-lock.json
    if [ -f "package-lock.json" ]; then
        rm -f package-lock.json
        log "已清理 package-lock.json"
    fi
    
    # 清理构建文件
    if [ -d "dist" ]; then
        rm -rf dist
        log "已清理 dist 目录"
    fi
    
    info "缓存清理完成"
}

# 主函数
main() {
    show_banner
    
    # 切换到frontend目录
    cd "$(dirname "$0")/.."
    
    # 处理命令行参数
    case "${1:-start}" in
        "start")
            check_node
            check_npm
            check_port
            check_backend
            check_config
            check_dependencies
            start_dev_server
            ;;
        "clean")
            clean_cache
            ;;
        "install")
            check_node
            check_npm
            install_dependencies
            ;;
        *)
            echo "使用方法: $0 {start|clean|install}"
            echo "  start   - 启动开发服务器"
            echo "  clean   - 清理缓存和依赖"
            echo "  install - 重新安装依赖"
            exit 1
            ;;
    esac
}

# 信号处理
trap 'echo -e "\n${YELLOW}正在停止开发服务器...${NC}"; exit 0' INT TERM

# 脚本入口
main "$@"
